 {% comment %}
  Key Features Section for Ideaformer IR3 V2 3D Printer
  File: sections/ir3-v2-key-features.liquid
{% endcomment %}

{{ 'ir3-v2-key-features.css' | asset_url | stylesheet_tag }}

<section
  class="key-features-section"
  id="key-features-{{ section.id }}"
  data-section-id="{{ section.id }}"
  style="margin-top: 0px; margin-bottom: {{ section.settings.margin_bottom }}px;"
>
  <!-- 背景图层 -->
  <div class="background-layer">
    <div class="gradient-overlay"></div>
    <div class="grid-pattern"></div>
    <div class="tech-lines"></div>
    <div class="floating-shapes">
      <div class="shape shape-1"></div>
      <div class="shape shape-2"></div>
      <div class="shape shape-3"></div>
    </div>
  </div>

  <!-- Dynamic 3D Printing Elements -->
  <div class="printing-particles"></div>
  <div class="tech-circuits">
    <div class="circuit circuit-1"></div>
    <div class="circuit circuit-2"></div>
    <div class="circuit circuit-3"></div>
  </div>
  <div class="floating-icons">
    <div class="print-icon icon-1">⚙️</div>
    <div class="print-icon icon-2">🔧</div>
    <div class="print-icon icon-3">📐</div>
    <div class="print-icon icon-4">🎯</div>
  </div>
  <div class="energy-beams">
    <div class="beam beam-1"></div>
    <div class="beam beam-2"></div>
  </div>

  <!-- Content Container -->
  <div class="features-container" style="padding-top: 0; margin-top: 0;">
    <!-- Title Group -->
    <div class="title-group" data-aos="fade-up">
      <h2 class="main-title glitch" data-text="{{ section.settings.main_title | default: 'Key Features' }}">{{ section.settings.main_title | default: 'Key Features' }}</h2>
      <div class="title-underline"></div>
      <p class="subtitle">{{ section.settings.subtitle | default: 'Experience three groundbreaking innovations in a single machine: endless batch production, unlimited Z-axis printing, and support-free overhang technology.' }}</p>
    </div>

    <!-- Feature Tags - Moved above feature content -->
    <div class="feature-tags">
      <button class="feature-tag active" data-feature="batch">
        <span class="tag-text">{{ section.settings.tag_1_text | default: "Batch Production" }}</span>
        <div class="tag-bg"></div>
      </button>
      <button class="feature-tag" data-feature="unlimited">
        <span class="tag-text">{{ section.settings.tag_2_text | default: "Unlimited Z-Axis" }}</span>
        <div class="tag-bg"></div>
      </button>
      <button class="feature-tag" data-feature="support-free">
        <span class="tag-text">{{ section.settings.tag_4_text | default: "Support-Free" }}</span>
        <div class="tag-bg"></div>
      </button>
    </div>

    <!-- Feature Display -->
    <div class="feature-showcase">
      <!-- Left Navigation Button -->
      <button class="feature-nav-button prev-button" aria-label="Previous feature">
        <div class="button-bg"></div>
        <svg width="28" height="28" viewBox="0 0 24 24" fill="none">
          <path d="M15 6L9 12L15 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </button>

      <!-- Feature Content -->
      <div class="feature-content-wrap">
        <!-- Feature Images -->
        <div class="feature-image-container">
          <div class="feature-images">
            <img class="feature-img active"
                 data-feature="batch"
                 src="https://cdn.shopify.com/s/files/1/0762/6113/0493/files/2_666d80de-e5e9-4876-9359-65c297d39b1d.png?v=1752026723"
                 alt="{{ section.settings.feature_1_title | default: 'Unlimited Batch Production' }}"
                 loading="eager"
                 fetchpriority="high"
                 decoding="async">

            <img class="feature-img"
                 data-feature="unlimited"
                 src="https://cdn.shopify.com/s/files/1/0762/6113/0493/files/3_3a4e5883-448e-4355-ae3c-5ec9d8ef11ca.png?v=1752026722"
                 alt="{{ section.settings.feature_2_title | default: 'Break the Z-Axis Limitation' }}"
                 loading="lazy"
                 decoding="async">

            <img class="feature-img"
                 data-feature="support-free"
                 src="https://cdn.shopify.com/s/files/1/0762/6113/0493/files/4_0998892e-0f93-4450-a5e0-22f5e068391d.png?v=1752044795"
                 alt="{{ section.settings.feature_3_title | default: 'Support-Free Overhang Printing' }}"
                 loading="lazy"
                 decoding="async">
          </div>
        </div>

        <!-- Feature Text Blocks -->
        <div class="feature-text-container">
          <div class="feature-texts">
            <!-- Feature 1 Text -->
            <div class="feature-text active" data-feature="batch">
              <span class="feature-number">01</span>
              <h3 class="feature-title">{{ section.settings.feature_1_title | default: "Unlimited Batch Production" }}</h3>
              <p class="feature-description">{{ section.settings.feature_1_description | default: "Repeat print the same model multiple times or print multiple models at once. Test and adjust slice settings first, then start mass production." }}</p>
              <ul class="feature-bullets">
                <li>{{ section.settings.feature_1_bullet_1 | default: "Multiple models in one batch" }}</li>
                <li>{{ section.settings.feature_1_bullet_2 | default: "Perfect for mass production" }}</li>
                <li>{{ section.settings.feature_1_bullet_3 | default: "Consistent quality across prints" }}</li>
              </ul>
            </div>

            <!-- Feature 2 Text -->
            <div class="feature-text" data-feature="unlimited">
              <span class="feature-number">02</span>
              <h3 class="feature-title">{{ section.settings.feature_2_title | default: "Break the Z-Axis Limitation" }}</h3>
              <p class="feature-description">{{ section.settings.feature_2_description | default: "Print models without length restrictions. Perfect for architectural models, long tools, and oversized prototypes." }}</p>
              <ul class="feature-bullets">
                <li>{{ section.settings.feature_2_bullet_1 | default: "Unlimited Length printing" }}</li>
                <li>{{ section.settings.feature_2_bullet_2 | default: "Perfect for architectural models" }}</li>
                <li>{{ section.settings.feature_2_bullet_3 | default: "No Z-axis height constraints" }}</li>
              </ul>
            </div>

            <!-- Feature 3 Text -->
            <div class="feature-text" data-feature="support-free">
              <span class="feature-number">03</span>
              <h3 class="feature-title">{{ section.settings.feature_3_title | default: "Support-Free Overhang Printing" }}</h3>
              <p class="feature-description">{{ section.settings.feature_3_description | default: "Back printing angles don't need support structures, saving material and post-processing time." }}</p>
              <ul class="feature-bullets">
                <li>{{ section.settings.feature_3_bullet_1 | default: "No Support Needed for overhangs" }}</li>
                <li>{{ section.settings.feature_3_bullet_2 | default: "Saves material and post-processing time" }}</li>
                <li>{{ section.settings.feature_3_bullet_3 | default: "Superior surface finish quality" }}</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      <!-- Right Navigation Button -->
      <button class="feature-nav-button next-button" aria-label="Next feature">
        <div class="button-bg"></div>
        <svg width="28" height="28" viewBox="0 0 24 24" fill="none">
          <path d="M9 6L15 12L9 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </button>
    </div>


  </div>
</section>

<style>
/* Override any margin issues */
.key-features-section {
  margin-top: 0 !important;
}

/* Scroll Continue Indicator */
.scroll-continue-indicator {
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 100;
  opacity: 0;
  transition: opacity 0.5s ease;
  pointer-events: none;
}

.scroll-continue-indicator.visible {
  opacity: 1;
}

.scroll-continue-indicator .indicator-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 149, 0, 0.3);
  border-radius: 20px;
  padding: 15px 25px;
  color: #fff;
}

.scroll-continue-indicator .indicator-icon {
  font-size: 24px;
  margin-bottom: 8px;
  animation: bounce 2s infinite;
  color: #ff9500;
}

.scroll-continue-indicator .indicator-text {
  font-size: 14px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
  text-align: center;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

/* Mobile adjustments */
@media screen and (max-width: 768px) {
  .scroll-continue-indicator {
    bottom: 20px;
  }

  .scroll-continue-indicator .indicator-content {
    padding: 12px 20px;
  }

  .scroll-continue-indicator .indicator-icon {
    font-size: 20px;
  }

  .scroll-continue-indicator .indicator-text {
    font-size: 12px;
  }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const keyFeaturesSection = document.getElementById('key-features-{{ section.id }}');
  if (!keyFeaturesSection) return;

  // Initialize GSAP if available
  const hasGsap = typeof gsap !== 'undefined';
  const hasScrollTrigger = hasGsap && typeof ScrollTrigger !== 'undefined';

  if (hasGsap && hasScrollTrigger) {
    gsap.registerPlugin(ScrollTrigger);
  }

  console.log('=== IR3 Key Features Simplified Version ===');
  console.log('GSAP available:', hasGsap);
  console.log('ScrollTrigger available:', hasScrollTrigger);

  // Elements
  const prevButton = keyFeaturesSection.querySelector('.prev-button');
  const nextButton = keyFeaturesSection.querySelector('.next-button');
  const featureTags = keyFeaturesSection.querySelectorAll('.feature-tag');
  const featureImages = keyFeaturesSection.querySelectorAll('.feature-img');
  const featureTexts = keyFeaturesSection.querySelectorAll('.feature-text');

  // Feature data
  const features = ['batch', 'unlimited', 'support-free'];
  let currentIndex = 0;
  let isAnimating = false;

  console.log('Found elements:', {
    prevButton: !!prevButton,
    nextButton: !!nextButton,
    featureTags: featureTags.length,
    featureImages: featureImages.length,
    featureTexts: featureTexts.length
  });

  // Enhanced change feature function with clear animations
  function changeFeature(index) {
    if (isAnimating) return;

    // Ensure index is within range
    if (index < 0) index = features.length - 1;
    if (index >= features.length) index = 0;

    const prevIndex = currentIndex;
    currentIndex = index;
    isAnimating = true;

    console.log(`Changing feature from ${prevIndex} to ${currentIndex}`);

    // Use GSAP for smooth transitions if available
    if (hasGsap) {
      const tl = gsap.timeline({
        onComplete: () => {
          isAnimating = false;
          console.log(`Feature change to ${currentIndex} completed`);
        }
      });

      // 1. Animate tags with immediate visual feedback
      tl.to(featureTags, {
        scale: 0.9,
        duration: 0.15,
        ease: "power2.in",
        onComplete: () => {
          // Update tag active states
          featureTags.forEach(tag => {
            const tagFeature = tag.getAttribute('data-feature');
            tag.classList.toggle('active', tagFeature === features[currentIndex]);
          });
        }
      })
      .to(featureTags, {
        scale: 1,
        duration: 0.25,
        ease: "back.out(1.7)"
      });

      // 2. Animate images with smooth transitions - avoid CSS class conflicts
      const nextIndex = (currentIndex + 1) % features.length;
      const previousIndex = (currentIndex - 1 + features.length) % features.length;

      // Completely disable CSS interference during animation
      featureImages.forEach((img) => {
        img.classList.remove('active', 'prev', 'next');
        img.style.transition = 'none';
        img.style.willChange = 'transform, opacity';
      });

      // Create smooth image transitions with proper timing
      featureImages.forEach((img, i) => {
        let targetState = {};

        if (i === currentIndex) {
          // Active image
          targetState = {
            opacity: 1,
            scale: 1.3,
            x: '0%',
            rotateY: 0,
            zIndex: 5,
            filter: 'blur(0px) brightness(1)'
          };
        } else if (i === previousIndex) {
          // Previous image
          targetState = {
            opacity: 0.15,
            scale: 0.7,
            x: '-30%',
            rotateY: -15,
            zIndex: 2,
            filter: 'blur(2px) brightness(0.7)'
          };
        } else if (i === nextIndex) {
          // Next image
          targetState = {
            opacity: 0.15,
            scale: 0.7,
            x: '30%',
            rotateY: 15,
            zIndex: 2,
            filter: 'blur(2px) brightness(0.7)'
          };
        } else {
          // Hidden images - smooth fade to zero
          targetState = {
            opacity: 0,
            scale: 0.5,
            x: '0%',
            rotateY: 0,
            zIndex: 1,
            filter: 'blur(4px) brightness(0.5)'
          };
        }

        // Animate with consistent timing for all images
        tl.to(img, {
          ...targetState,
          duration: 1.0, // Longer duration for smoother transition
          ease: "power2.inOut", // Smoother easing
          force3D: true,
          transformOrigin: "center center",
          // No onStart/onComplete to avoid interruptions
        }, "<"); // All images animate simultaneously
      });

      // Only add CSS classes after ALL animations complete
      tl.call(() => {
        featureImages.forEach((img, i) => {
          if (i === currentIndex) {
            img.classList.add('active');
          } else if (i === previousIndex) {
            img.classList.add('prev');
          } else if (i === nextIndex) {
            img.classList.add('next');
          }
          img.style.transition = '';
          img.style.willChange = 'auto';
        });
      });

      // 3. Animate text content
      // First hide current text
      tl.to(featureTexts, {
        opacity: 0,
        x: -30,
        duration: 0.25,
        ease: "power2.in",
        onComplete: () => {
          // Update text display states
          featureTexts.forEach((text, i) => {
            if (i !== currentIndex) {
              text.style.display = 'none';
              text.classList.remove('active');
            } else {
              text.style.display = 'block';
              text.classList.add('active');
            }
          });
        }
      }, "<");

      // Then show new text
      tl.fromTo(featureTexts[currentIndex], {
        opacity: 0,
        x: 30,
        y: 10
      }, {
        opacity: 1,
        x: 0,
        y: 0,
        duration: 0.5,
        ease: "power2.out"
      });

    } else {
      // Fallback without GSAP - basic transitions
      featureTags.forEach(tag => {
        const tagFeature = tag.getAttribute('data-feature');
        tag.classList.toggle('active', tagFeature === features[currentIndex]);
      });

      featureImages.forEach((img, i) => {
        img.classList.toggle('active', i === currentIndex);
      });

      featureTexts.forEach((text, i) => {
        text.classList.toggle('active', i === currentIndex);
      });

      isAnimating = false;
    }
  }

  // Simplified scroll lock system with ScrollTrigger
  let isScrollLocked = false;
  let scrollLockPosition = 0;
  let viewedFeatures = new Set([0]); // Start with first feature viewed

  function initScrollTrigger() {
    if (!hasScrollTrigger) {
      console.log('ScrollTrigger not available, using fallback');
      initializeFeatures();
      return;
    }

    console.log('Initializing ScrollTrigger with scrub control like scroll.html...');

    // Create timeline for feature switching - following scroll.html pattern
    const featureTimeline = gsap.timeline({
      scrollTrigger: {
        trigger: keyFeaturesSection,
        start: "top top",
        end: "+=3000", // Enough distance for 3 features
        pin: true,
        scrub: 1, // Key: smooth sync with scroll like scroll.html
        onUpdate: (self) => {
          // Calculate which feature should be active based on scroll progress
          const progress = self.progress;
          const featureIndex = Math.floor(progress * features.length);
          const clampedIndex = Math.min(featureIndex, features.length - 1);

          console.log(`ScrollTrigger progress: ${progress.toFixed(3)}, feature: ${clampedIndex}`);

          // Change feature if different from current and not animating
          if (clampedIndex !== currentIndex && !isAnimating) {
            changeFeature(clampedIndex);
            viewedFeatures.add(clampedIndex);
          }
        },
        onEnter: () => {
          console.log('Entered Key Features section');
          isScrollLocked = true;
        },
        onLeave: () => {
          console.log('Left Key Features section');
          isScrollLocked = false;
        }
      }
    });

    // Add dummy animation to the timeline to make scrub work
    featureTimeline.to({}, { duration: 3 }); // 3 seconds for 3 features

    console.log('ScrollTrigger initialized with scrub control');
  }

  // Robust scroll handling with strict section detection
  let lastScrollTime = 0;
  let isInKeyFeaturesSection = false;

  function handleScrollInSection(e) {
    // Check if we're actually in the key features section
    const rect = keyFeaturesSection.getBoundingClientRect();
    const isInViewport = rect.top <= 0 && rect.bottom >= window.innerHeight;

    // Only handle scroll if we're in the section AND it's locked
    if (!isInViewport || !isScrollLocked || isAnimating) {
      return; // Let normal scroll behavior continue
    }

    // We're in the section - prevent default scrolling
    e.preventDefault();
    e.stopPropagation();

    const now = Date.now();
    const delta = e.deltaY;

    // Ignore very small movements
    if (Math.abs(delta) < 20) return;

    // Strict throttling to prevent rapid switching
    if (now - lastScrollTime < 1000) {
      console.log('Scroll throttled - too fast');
      return;
    }

    lastScrollTime = now;
    const scrollDirection = delta > 0 ? 'down' : 'up';

    console.log(`Scroll in locked section: direction=${scrollDirection}, currentIndex=${currentIndex}, viewedFeatures=${Array.from(viewedFeatures)}`);

    if (scrollDirection === 'down') {
      // Scrolling down - next feature
      const nextIndex = currentIndex + 1;
      if (nextIndex < features.length) {
        console.log(`Switching to feature ${nextIndex}`);
        changeFeature(nextIndex);
        viewedFeatures.add(nextIndex);
      } else {
        // At last feature - check if all viewed
        if (viewedFeatures.size >= features.length) {
          console.log('All features viewed - unlocking scroll');
          isScrollLocked = false;
          // Allow the scroll event to continue naturally
          setTimeout(() => {
            // Don't re-lock automatically
          }, 100);
        } else {
          console.log(`Cannot exit - viewed ${viewedFeatures.size}/${features.length} features`);
        }
      }
    } else {
      // Scrolling up - previous feature
      const prevIndex = currentIndex - 1;
      if (prevIndex >= 0) {
        console.log(`Switching to feature ${prevIndex}`);
        changeFeature(prevIndex);
        viewedFeatures.add(prevIndex);
      } else {
        // At first feature - check if all viewed before allowing exit
        if (viewedFeatures.size >= features.length) {
          console.log('All features viewed - allowing scroll up');
          isScrollLocked = false;
        } else {
          console.log('Cannot scroll up - not all features viewed');
        }
      }
    }
  }

  // Button event handlers
  function initializeEventHandlers() {
    console.log('Initializing event handlers...');

    // Add scroll event listener for manual feature switching
    document.addEventListener('wheel', handleScrollInSection, { passive: false });

    // Previous button with click animation
    if (prevButton) {
      prevButton.addEventListener('click', () => {
        // Immediate button feedback animation
        if (hasGsap) {
          gsap.to(prevButton, {
            scale: 0.85,
            duration: 0.1,
            ease: "power2.in",
            onComplete: () => {
              gsap.to(prevButton, {
                scale: 1,
                duration: 0.3,
                ease: "elastic.out(1, 0.5)"
              });
            }
          });
        }

        const prevIndex = (currentIndex - 1 + features.length) % features.length;
        changeFeature(prevIndex);
        viewedFeatures.add(prevIndex);
      });
    }

    // Next button with click animation
    if (nextButton) {
      nextButton.addEventListener('click', () => {
        // Immediate button feedback animation
        if (hasGsap) {
          gsap.to(nextButton, {
            scale: 0.85,
            duration: 0.1,
            ease: "power2.in",
            onComplete: () => {
              gsap.to(nextButton, {
                scale: 1,
                duration: 0.3,
                ease: "elastic.out(1, 0.5)"
              });
            }
          });
        }

        const nextIndex = (currentIndex + 1) % features.length;
        changeFeature(nextIndex);
        viewedFeatures.add(nextIndex);
      });
    }

    // Feature tags with click animation
    featureTags.forEach((tag, index) => {
      tag.addEventListener('click', () => {
        // Immediate tag feedback animation
        if (hasGsap) {
          gsap.to(tag, {
            scale: 0.9,
            duration: 0.1,
            ease: "power2.in",
            onComplete: () => {
              gsap.to(tag, {
                scale: 1,
                duration: 0.25,
                ease: "back.out(1.7)"
              });
            }
          });
        }

        changeFeature(index);
        viewedFeatures.add(index);
      });
    });

    console.log('Event handlers initialized successfully');
  }

  // Initialize features with proper states and styles
  function initializeFeatures() {
    console.log('Initializing IR3 Key Features...');

    // Set initial states for all images with GSAP
    featureImages.forEach((img, i) => {
      img.classList.remove('active', 'prev', 'next');

      if (hasGsap) {
        // Disable CSS transitions during initialization
        img.style.transition = 'none';

        if (i === 0) {
          // Active image - set with GSAP first, then add class
          gsap.set(img, {
            opacity: 1,
            scale: 1.3,
            x: 0,
            rotateY: 0,
            force3D: true,
            onComplete: () => {
              img.classList.add('active');
              img.style.transition = '';
            }
          });
        } else if (i === 1) {
          // Next image
          gsap.set(img, {
            opacity: 0.15,
            scale: 0.7,
            x: '30%',
            rotateY: '15deg',
            force3D: true,
            onComplete: () => {
              img.classList.add('next');
              img.style.transition = '';
            }
          });
        } else if (i === 2) {
          // Previous image
          gsap.set(img, {
            opacity: 0.15,
            scale: 0.7,
            x: '-30%',
            rotateY: '-15deg',
            force3D: true,
            onComplete: () => {
              img.classList.add('prev');
              img.style.transition = '';
            }
          });
        }
      } else {
        // Fallback without GSAP
        if (i === 0) {
          img.classList.add('active');
        } else if (i === 1) {
          img.classList.add('next');
        } else if (i === 2) {
          img.classList.add('prev');
        }
      }
    });

    // Set initial states for text blocks
    featureTexts.forEach((text, i) => {
      text.classList.toggle('active', i === 0);
      if (hasGsap) {
        if (i === 0) {
          gsap.set(text, { opacity: 1, x: 0, y: 0 });
        } else {
          gsap.set(text, { opacity: 0, x: 30 });
        }
      }
    });

    // Set initial states for tags
    featureTags.forEach((tag, i) => {
      tag.classList.toggle('active', i === 0);
      if (hasGsap) {
        gsap.set(tag, { scale: 1 });
      }
    });

    console.log('IR3 Key Features initialized successfully');
  }

  // Check if all features have been viewed
  function checkAllFeaturesViewed() {
    if (viewedFeatures.size >= features.length) {
      console.log('All features viewed! User can now scroll past the section.');
      // ScrollTrigger will automatically handle the unlock when reaching the end
      return true;
    }
    return false;
  }

  // Enhanced change feature function with viewing tracking
  function changeFeatureWithTracking(index) {
    changeFeature(index);
    viewedFeatures.add(index);

    console.log(`Feature ${index} viewed. Total viewed: ${viewedFeatures.size}/${features.length}`);

    if (checkAllFeaturesViewed()) {
      // Show completion indicator or update UI
      console.log('All features have been explored!');
    }
  }

  // Main initialization function
  function init() {
    console.log('Starting IR3 Key Features initialization...');

    // Initialize features first
    initializeFeatures();

    // Initialize event handlers
    initializeEventHandlers();

    // Initialize ScrollTrigger if available
    if (hasScrollTrigger) {
      initScrollTrigger();
    } else {
      console.log('ScrollTrigger not available, using basic functionality');
    }

    console.log('IR3 Key Features initialization completed');
    console.log('Scroll lock system: Enabled with feature switching');
  }

  // Start initialization
  init();
});
</script>

{% schema %}
{
  "name": "IR3 V2 Key Features",
  "tag": "section",
  "class": "ir3-key-features-section",
  "settings": [
    {
      "type": "header",
      "content": "Title Settings"
    },
    {
      "type": "text",
      "id": "main_title",
      "label": "Main Title",
      "default": "Key Features"
    },
    {
      "type": "textarea",
      "id": "subtitle",
      "label": "Subtitle",
      "default": "Experience three groundbreaking innovations in a single machine: endless batch production, unlimited Z-axis printing, and support-free overhang technology."
    },
    {
      "type": "header",
      "content": "Feature 1 Settings"
    },
    {
      "type": "text",
      "id": "feature_1_title",
      "label": "Feature 1 Title",
      "default": "Unlimited Batch Production"
    },
    {
      "type": "textarea",
      "id": "feature_1_description",
      "label": "Feature 1 Description",
      "default": "Repeat print the same model multiple times or print multiple models at once. Test and adjust slice settings first, then start mass production."
    },
    {
      "type": "text",
      "id": "feature_1_bullet_1",
      "label": "Feature 1 Bullet Point 1",
      "default": "Multiple models in one batch"
    },
    {
      "type": "text",
      "id": "feature_1_bullet_2",
      "label": "Feature 1 Bullet Point 2",
      "default": "Perfect for mass production"
    },
    {
      "type": "text",
      "id": "feature_1_bullet_3",
      "label": "Feature 1 Bullet Point 3",
      "default": "Consistent quality across prints"
    },
    {
      "type": "image_picker",
      "id": "feature_1_image",
      "label": "Feature 1 Image"
    },
    {
      "type": "header",
      "content": "Feature 2 Settings"
    },
    {
      "type": "text",
      "id": "feature_2_title",
      "label": "Feature 2 Title",
      "default": "Break the Z-Axis Limitation"
    },
    {
      "type": "textarea",
      "id": "feature_2_description",
      "label": "Feature 2 Description",
      "default": "Print models without length restrictions. Perfect for architectural models, long tools, and oversized prototypes."
    },
    {
      "type": "text",
      "id": "feature_2_bullet_1",
      "label": "Feature 2 Bullet Point 1",
      "default": "Unlimited Length printing"
    },
    {
      "type": "text",
      "id": "feature_2_bullet_2",
      "label": "Feature 2 Bullet Point 2",
      "default": "Perfect for architectural models"
    },
    {
      "type": "text",
      "id": "feature_2_bullet_3",
      "label": "Feature 2 Bullet Point 3",
      "default": "No Z-axis height constraints"
    },
    {
      "type": "image_picker",
      "id": "feature_2_image",
      "label": "Feature 2 Image"
    },
    {
      "type": "header",
      "content": "Feature 3 Settings"
    },
    {
      "type": "text",
      "id": "feature_3_title",
      "label": "Feature 3 Title",
      "default": "Support-Free Overhang Printing"
    },
    {
      "type": "textarea",
      "id": "feature_3_description",
      "label": "Feature 3 Description",
      "default": "Back printing angles don't need support structures, saving material and post-processing time."
    },
    {
      "type": "text",
      "id": "feature_3_bullet_1",
      "label": "Feature 3 Bullet Point 1",
      "default": "No Support Needed for overhangs"
    },
    {
      "type": "text",
      "id": "feature_3_bullet_2",
      "label": "Feature 3 Bullet Point 2",
      "default": "Saves material and post-processing time"
    },
    {
      "type": "text",
      "id": "feature_3_bullet_3",
      "label": "Feature 3 Bullet Point 3",
      "default": "Superior surface finish quality"
    },
    {
      "type": "image_picker",
      "id": "feature_3_image",
      "label": "Feature 3 Image"
    },
    {
      "type": "header",
      "content": "Feature Tags"
    },
    {
      "type": "text",
      "id": "tag_1_text",
      "label": "Tag 1 Text",
      "default": "Batch"
    },
    {
      "type": "text",
      "id": "tag_2_text",
      "label": "Tag 2 Text",
      "default": "Unlimited"
    },
    {
      "type": "text",
      "id": "tag_3_text",
      "label": "Tag 3 Text",
      "default": "Printing"
    },
    {
      "type": "text",
      "id": "tag_4_text",
      "label": "Tag 4 Text",
      "default": "Support-Free"
    },
    {
      "type": "header",
      "content": "Spacing Settings"
    },
    {
      "type": "number",
      "id": "margin_top",
      "label": "Top Margin (px)",
      "default": 0
    },
    {
      "type": "number",
      "id": "margin_bottom",
      "label": "Bottom Margin (px)",
      "default": 0
    }
  ],
  "presets": [
    {
      "name": "IR3 V2 Key Features"
    }
  ]
}
{% endschema %} 