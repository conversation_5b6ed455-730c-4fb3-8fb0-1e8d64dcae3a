/* Hero Section for Ideaformer IR3 V2 3D Printer - Enhanced Version */
/* File: assets/IR3-hero-section-1.css */

/* Hero Section Base Styles */
.hero-section {
  position: relative;
  min-height: 100vh;
  overflow: hidden;
  background: #000;
  background-image: url('https://cdn.shopify.com/s/files/1/0799/0011/3809/files/1_e45deba7-e16d-46d1-bb32-4e316a79d4d0.png?v=1735547234');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.hero-container {
  position: relative;
  width: 100%;
  height: 100vh;
  display: flex;
  align-items: center;
}

/* Enhanced Background Layers */
.background-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.gradient-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(ellipse at top, #1a1a2e 0%, #0a0a0a 50%, #000 100%);
  opacity: 0.9;
}

.animated-gradient {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    45deg,
    transparent 0%,
    rgba(66, 165, 245, 0.1) 25%,
    transparent 50%,
    rgba(29, 233, 182, 0.1) 75%,
    transparent 100%
  );
  background-size: 400% 400%;
  animation: gradientShift 15s ease infinite;
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.grid-pattern {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(rgba(255, 255, 255, 0.03) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.03) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: gridMove 20s linear infinite;
}

@keyframes gridMove {
  0% {
    transform: translate(0, 0);
  }
  100% {
    transform: translate(50px, 50px);
  }
}

.tech-lines {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    repeating-linear-gradient(
      90deg,
      transparent,
      transparent 100px,
      rgba(66, 165, 245, 0.03) 100px,
      rgba(66, 165, 245, 0.03) 101px
    ),
    repeating-linear-gradient(
      0deg,
      transparent,
      transparent 100px,
      rgba(29, 233, 182, 0.03) 100px,
      rgba(29, 233, 182, 0.03) 101px
    );
  animation: linesMove 30s linear infinite;
}

@keyframes linesMove {
  0% {
    transform: translate(0, 0);
  }
  100% {
    transform: translate(-100px, -100px);
  }
}

/* Floating Shapes */
.floating-shapes {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.shape {
  position: absolute;
  border-radius: 50%;
  filter: blur(40px);
  opacity: 0.3;
}

.shape-1 {
  width: 400px;
  height: 400px;
  background: radial-gradient(circle, #42a5f5 0%, transparent 70%);
  top: -200px;
  right: -100px;
  animation: float1 20s ease-in-out infinite;
}

.shape-2 {
  width: 300px;
  height: 300px;
  background: radial-gradient(circle, #1de9b6 0%, transparent 70%);
  bottom: -150px;
  left: -100px;
  animation: float2 25s ease-in-out infinite;
}

.shape-3 {
  width: 250px;
  height: 250px;
  background: radial-gradient(circle, #7c4dff 0%, transparent 70%);
  top: 50%;
  left: 50%;
  animation: float3 30s ease-in-out infinite;
}

@keyframes float1 {
  0%,
  100% {
    transform: translate(0, 0) rotate(0deg) scale(1);
  }
  25% {
    transform: translate(30px, -50px) rotate(90deg) scale(1.1);
  }
  50% {
    transform: translate(-20px, -30px) rotate(180deg) scale(0.9);
  }
  75% {
    transform: translate(40px, -10px) rotate(270deg) scale(1.05);
  }
}

@keyframes float2 {
  0%,
  100% {
    transform: translate(0, 0) rotate(0deg) scale(1);
  }
  33% {
    transform: translate(-40px, -30px) rotate(120deg) scale(1.08);
  }
  66% {
    transform: translate(-25px, -50px) rotate(240deg) scale(0.92);
  }
}

@keyframes float3 {
  0%,
  100% {
    transform: translate(0, 0) rotate(0deg) scale(1);
  }
  20% {
    transform: translate(50px, -25px) rotate(72deg) scale(1.12);
  }
  40% {
    transform: translate(30px, -45px) rotate(144deg) scale(0.88);
  }
  60% {
    transform: translate(45px, -10px) rotate(216deg) scale(1.06);
  }
  80% {
    transform: translate(15px, -35px) rotate(288deg) scale(0.94);
  }
}

/* Particle Field */
.particle-field {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.particle-field::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(2px 2px at 20px 30px, rgba(255, 255, 255, 0.3), transparent),
    radial-gradient(2px 2px at 40px 70px, rgba(255, 255, 255, 0.2), transparent),
    radial-gradient(1px 1px at 50px 50px, rgba(255, 255, 255, 0.3), transparent),
    radial-gradient(1px 1px at 80px 10px, rgba(255, 255, 255, 0.2), transparent),
    radial-gradient(2px 2px at 130px 80px, rgba(255, 255, 255, 0.3), transparent);
  background-repeat: repeat;
  background-size: 300px 300px;
  animation: particleAnimation 100s linear infinite;
}

@keyframes particleAnimation {
  from {
    transform: translate(0, 0) rotate(0deg);
  }
  to {
    transform: translate(-300px, -300px) rotate(360deg);
  }
}

/* Content Layer */
.content-layer {
  position: relative;
  z-index: 2;
  width: 100%;
  padding: 2rem;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
}

/* Hero Content */
.hero-content {
  color: #fff;
}

.title-group {
  margin-bottom: 2rem;
}

.pre-title {
  display: inline-block;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.2em;
  color: #42a5f5;
  margin-bottom: 1rem;
  position: relative;
  padding-left: 2rem;
}

.pre-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 1.5rem;
  height: 1px;
  background: #42a5f5;
}

.main-title {
  font-size: clamp(1.8rem, 5vw, 4.5rem);
  font-weight: 900;
  line-height: 1;
  margin-bottom: 0.5rem;
  letter-spacing: -0.02em;
  position: relative;
  background: linear-gradient(135deg, #fff 0%, #42a5f5 50%, #1de9b6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  white-space: nowrap;
}

/* Glitch Effect */
.glitch {
  position: relative;
}

.glitch::before,
.glitch::after {
  content: attr(data-text);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #fff 0%, #42a5f5 50%, #1de9b6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.glitch::before {
  animation: glitch-1 0.5s infinite;
  color: #42a5f5;
  z-index: -1;
}

.glitch::after {
  animation: glitch-2 0.5s infinite;
  color: #1de9b6;
  z-index: -2;
}

@keyframes glitch-1 {
  0%,
  100% {
    clip-path: inset(0 0 0 0);
    transform: translate(0);
  }
  20% {
    clip-path: inset(0 100% 0 0);
    transform: translate(-2px);
  }
  40% {
    clip-path: inset(0 0 0 100%);
    transform: translate(2px);
  }
  60% {
    clip-path: inset(100% 0 0 0);
    transform: translate(-1px);
  }
  80% {
    clip-path: inset(0 0 100% 0);
    transform: translate(1px);
  }
}

@keyframes glitch-2 {
  0%,
  100% {
    clip-path: inset(0 0 0 0);
    transform: translate(0);
  }
  20% {
    clip-path: inset(100% 0 0 0);
    transform: translate(1px);
  }
  40% {
    clip-path: inset(0 0 100% 0);
    transform: translate(-1px);
  }
  60% {
    clip-path: inset(0 100% 0 0);
    transform: translate(2px);
  }
  80% {
    clip-path: inset(0 0 0 100%);
    transform: translate(-2px);
  }
}

.sub-title {
  font-size: clamp(1.25rem, 2.5vw, 1.875rem);
  font-weight: 300;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 1rem;
  letter-spacing: 0.02em;
}

.tagline {
  font-size: 1.125rem;
  color: rgba(255, 255, 255, 0.6);
  line-height: 1.6;
}

/* Feature Pills */
.feature-pills {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.pill {
  display: inline-flex;
  align-items: center;
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 50px;
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.pill:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

/* Enhanced CTA Buttons */
.cta-group {
  display: flex;
  gap: 1rem;
  margin-bottom: 3rem;
  flex-wrap: wrap;
  margin-top: -20rem;
}

.primary-button {
  position: relative;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1.25rem 2.5rem;
  font-size: 1rem;
  font-weight: 600;
  text-decoration: none;
  border-radius: 60px;
  overflow: hidden;
  background: linear-gradient(135deg, #42a5f5 0%, #1e88e5 100%);
  color: #fff;
  transition: all 0.3s ease;
  z-index: 1;
}

.button-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #1de9b6 0%, #00bfa5 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.primary-button:hover .button-bg {
  opacity: 1;
}

.primary-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(66, 165, 245, 0.4);
}

.secondary-button {
  position: relative;
  display: inline-flex;
  align-items: center;
  padding: 0;
  font-size: 1rem;
  font-weight: 600;
  text-decoration: none;
  color: #fff;
  overflow: hidden;
}

.button-inner {
  position: relative;
  display: flex;
  align-items: center;
  padding: 1.25rem 2.5rem;
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 60px;
  transition: all 0.3s ease;
}

.button-border {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 2px solid transparent;
  border-radius: 60px;
  background: linear-gradient(135deg, #42a5f5, #1de9b6) border-box;
  -webkit-mask:
    linear-gradient(#fff 0 0) padding-box,
    linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.secondary-button:hover .button-border {
  opacity: 1;
}

.secondary-button:hover .button-inner {
  background: rgba(255, 255, 255, 0.05);
  transform: translateY(-2px);
}

/* Enhanced Scroll Indicator */
.scroll-indicator {
  display: flex;
  align-items: center;
  gap: 1rem;
  color: rgba(255, 255, 255, 0.5);
  font-size: 0.875rem;
  cursor: pointer;
  transition: color 0.3s ease;
  margin-top: -12rem;
}

.scroll-indicator,.cta-group{
  grid-column: 1; /* 第1列 */
  grid-row: 2;    /* 第2行 */
}

.scroll-indicator:hover {
  color: rgba(255, 255, 255, 0.8);
}

.mouse-icon {
  width: 24px;
  height: 36px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  position: relative;
}

.mouse-wheel {
  width: 4px;
  height: 8px;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 2px;
  position: absolute;
  top: 6px;
  left: 50%;
  transform: translateX(-50%);
  animation: mouseWheel 2s ease-in-out infinite;
}

@keyframes mouseWheel {
  0% {
    transform: translateX(-50%) translateY(0);
    opacity: 1;
  }
  100% {
    transform: translateX(-50%) translateY(12px);
    opacity: 0;
  }
}

/* Product Showcase */
.product-showcase {
  position: relative;
  perspective: 1000px;
}

.product-stage {
  position: relative;
  transform-style: preserve-3d;
  animation: stageRotate 8s ease-in-out infinite;
}

@keyframes stageRotate {
  0% {
    transform: rotateY(-5deg);
  }
  50% {
    transform: rotateY(5deg);
  }
  100% {
    transform: rotateY(-5deg);
  }
}

.product-stage:hover {
  animation-play-state: paused;
}

.product-image-wrapper {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.product-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  height: 80%;
  background: radial-gradient(circle, rgba(66, 165, 245, 0.4) 0%, transparent 70%);
  filter: blur(60px);
  animation: pulse 3s ease-in-out infinite;
}

@keyframes pulse {
  0%,
  100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.4;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.1);
    opacity: 0.6;
  }
}

.hero-product-img {
  width: 100%;
  height: auto;
  max-width: 600px;
  filter: drop-shadow(0 20px 40px rgba(0, 0, 0, 0.3));
  transition: transform 0.3s ease;
}

.product-stage:hover .hero-product-img {
  transform: scale(1.05);
}

.product-reflection {
  position: absolute;
  bottom: -50%;
  left: 50%;
  transform: translateX(-50%) scaleY(-1);
  width: 100%;
  height: 100%;
  opacity: 0.1;
  filter: blur(2px);
  mask-image: linear-gradient(to bottom, transparent 0%, black 100%);
}

/* Orbiting Elements */
.orbit-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 120%;
  height: 120%;
  pointer-events: none;
}

.orbit {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border: 1px solid rgba(66, 165, 245, 0.1);
  border-radius: 50%;
}

.orbit-1 {
  width: 100%;
  height: 100%;
  animation: orbit1 20s linear infinite;
}

.orbit-2 {
  width: 80%;
  height: 80%;
  animation: orbit2 15s linear infinite reverse;
}

@keyframes orbit1 {
  0% {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

@keyframes orbit2 {
  0% {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

.orbit-item {
  position: absolute;
  top: -20px;
  left: 50%;
  transform: translateX(-50%);
}

.icon-wrapper {
  width: 40px;
  height: 40px;
  background: rgba(66, 165, 245, 0.1);
  border: 1px solid rgba(66, 165, 245, 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  animation: iconPulse 2s ease-in-out infinite;
}

@keyframes iconPulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

.icon-wrapper svg {
  width: 20px;
  height: 20px;
  color: #42a5f5;
}

/* Enhanced Floating Badges */
.floating-badges {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.badge {
  position: absolute;
  background: rgba(255, 255, 255, 0.03);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 60px;
  padding: 0.875rem 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow:
    0 4px 20px rgba(0, 0, 0, 0.1),
    inset 0 1px 1px rgba(255, 255, 255, 0.1);
  overflow: hidden;
}

.badge-shine {
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    45deg,
    transparent 30%,
    rgba(255, 255, 255, 0.1) 50%,
    transparent 70%
  );
  animation: shine 3s ease-in-out infinite;
}

@keyframes shine {
  0% {
    transform: translateX(-100%) translateY(-100%);
  }
  100% {
    transform: translateX(100%) translateY(100%);
  }
}

.badge-1 {
  top: 10%;
  left: -10%;
  animation: complexFloat1 8s ease-in-out infinite;
}

.badge-2 {
  top: 50%;
  right: -5%;
  animation: complexFloat2 10s ease-in-out infinite;
}

.badge-3 {
  bottom: 20%;
  left: 5%;
  animation: complexFloat3 9s ease-in-out infinite;
}

@keyframes complexFloat1 {
  0%,
  100% {
    transform: translate(0, 0) rotate(-5deg) scale(1);
  }
  25% {
    transform: translate(20px, -30px) rotate(5deg) scale(1.05);
  }
  50% {
    transform: translate(-10px, -20px) rotate(-3deg) scale(0.95);
  }
  75% {
    transform: translate(15px, -10px) rotate(3deg) scale(1.02);
  }
}

@keyframes complexFloat2 {
  0%,
  100% {
    transform: translate(0, 0) rotate(5deg) scale(1);
  }
  33% {
    transform: translate(-25px, -20px) rotate(-5deg) scale(1.03);
  }
  66% {
    transform: translate(-15px, -30px) rotate(7deg) scale(0.97);
  }
}

@keyframes complexFloat3 {
  0%,
  100% {
    transform: translate(0, 0) rotate(-3deg) scale(1);
  }
  20% {
    transform: translate(30px, -15px) rotate(3deg) scale(1.04);
  }
  40% {
    transform: translate(20px, -25px) rotate(-2deg) scale(0.96);
  }
  60% {
    transform: translate(25px, -5px) rotate(4deg) scale(1.02);
  }
  80% {
    transform: translate(10px, -20px) rotate(-4deg) scale(0.98);
  }
}

/* Enhanced Spec Highlights */
.spec-highlights {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 2rem;
  background: rgba(255, 255, 255, 0.02);
  backdrop-filter: blur(20px) saturate(180%);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 100px;
  padding: 1.25rem 2.5rem;
  box-shadow:
    0 10px 40px rgba(0, 0, 0, 0.2),
    inset 0 1px 1px rgba(255, 255, 255, 0.1);
}

.spec-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.375rem;
  position: relative;
}

.spec-item::after {
  content: '';
  position: absolute;
  right: -1rem;
  top: 50%;
  transform: translateY(-50%);
  width: 1px;
  height: 60%;
  background: rgba(255, 255, 255, 0.1);
}

.spec-item:last-child::after {
  display: none;
}

.spec-value {
  font-size: 1.375rem;
  font-weight: 700;
  background: linear-gradient(135deg, #42a5f5 0%, #1de9b6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.spec-label {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.6);
  text-transform: uppercase;
  letter-spacing: 0.1em;
}

/* Enhanced Animation Classes */
.animate-fade-in {
  opacity: 0;
  transform: translateY(30px);
  animation: enhancedFadeInUp 1s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

.animate-slide-in {
  opacity: 0;
  transform: translateX(-30px);
  animation: slideInRight 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

@keyframes enhancedFadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-fade-in[data-delay='0.3'] {
  animation-delay: 0.3s;
}
.animate-fade-in[data-delay='0.4'] {
  animation-delay: 0.4s;
}
.animate-fade-in[data-delay='0.5'] {
  animation-delay: 0.5s;
}
.animate-fade-in[data-delay='0.7'] {
  animation-delay: 0.7s;
}
.animate-fade-in[data-delay='0.9'] {
  animation-delay: 0.9s;
}
.animate-fade-in[data-delay='1.1'] {
  animation-delay: 1.1s;
}

/* Responsive Design */

/* 🖥️ 中等屏幕适配 (1200px-1600px) - 针对小屏幕笔记本 */
@media (max-width: 1600px) and (min-width: 1200px) {
  .container {
    max-width: 90%;
    padding: 0 2rem;
  }

  .spec-highlights {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 1.5rem;
    background: rgba(255, 255, 255, 0.02);
    backdrop-filter: blur(20px) saturate(180%);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 80px;
    padding: 1rem 2rem;
    box-shadow:
      0 10px 40px rgba(0, 0, 0, 0.2),
      inset 0 1px 1px rgba(255, 255, 255, 0.1);
    max-width: 85%;
    box-sizing: border-box;
  }

  .spec-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    min-width: 0;
    flex: 1;
  }

  .spec-value {
    font-size: 1.1rem;
    font-weight: 700;
    color: #00d4ff;
    margin-bottom: 0.25rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .spec-label {
    font-size: 0.75rem;
    color: rgba(255, 255, 255, 0.7);
    text-align: center;
    line-height: 1.2;
  }

  .hero-content {
    max-width: 90%;
  }

  .main-title {
    font-size: 3.5rem;
    line-height: 1.1;
  }

  .sub-title {
    font-size: 1.3rem;
  }

  .tagline {
    font-size: 1rem;
    max-width: 90%;
  }
}

@media (max-width: 1024px) {
  .container {
    grid-template-columns: 1fr;
    text-align: center;
    gap: 2rem;
    display: flex;
    flex-direction: column;
  }

  .hero-content {
    order: 1;
    width: 100%;
    padding: 0 20px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
  }

  .title-group {
    order: 1;
  }

  .product-showcase {
    order: 2;
    margin-bottom: 2rem;
    position: relative;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .feature-pills {
    order: 3;
    justify-content: center;
    margin-bottom: 1.5rem;
  }

  .cta-group {
    order: 4;
    justify-content: center;
    margin-top: 1rem;
  }

  .scroll-indicator {
    order: 5;
    margin-top: 1.5rem;
  }

  .orbit-container {
    display: none;
  }

  .pre-title {
    padding-left: 0;
  }

  .pre-title::before {
    display: none;
  }

  .floating-badges {
    display: none !important;
  }
}

@media (max-width: 768px) {
  .hero-container {
    height: auto;
    min-height: auto;
    padding: 120px 0 60px;
    display: flex;
    flex-direction: column;
  }

  .content-layer {
    padding: 1rem;
    display: flex;
    flex-direction: column;
  }

  .container {
    display: flex;
    flex-direction: column;
    position: relative;
    align-items: center;
    text-align: center;
    width: 100%;
  }

  /* 创建一个新的容器来放置按钮和滚动指示器 */
  .hero-content::after {
    content: '';
    order: 20;
    display: block;
  }

  .hero-content {
    order: 1;
    display: flex;
    flex-direction: column;
    text-align: center;
    align-items: center;
    width: 100%;
  }

  .product-showcase {
    order: 2;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .cta-group {
    order: 3;
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-evenly;
    margin-top: 0.5rem;
    padding: 0 1rem;
    box-sizing: border-box;
  }

  .scroll-indicator {
    order: 4;
    justify-content: center;
    width: 100%;
    margin: 1rem auto;
    position: relative;
    display: flex;
    align-items: center;
  }

  .title-group {
    order: 1;
    margin-bottom: 1.5rem;
    text-align: center;
    width: 100%;
  }

  .main-title {
    font-size: 2.5rem;
    margin-top: 0;
    text-align: center;
    white-space: nowrap;
  }

  .sub-title {
    font-size: 1.25rem;
    text-align: center;
  }

  .tagline {
    margin-bottom: 1.5rem;
    text-align: center;
  }

  .feature-pills {
    order: 2;
    margin-bottom: 1.5rem;
    flex-wrap: nowrap;
    gap: 0.5rem;
    justify-content: center;
    width: 100%;
    display: flex;
  }

  .pill {
    font-size: 0.75rem;
    padding: 0.4rem 0.8rem;
    white-space: nowrap;
  }



  /* 让product-showcase插入到hero-content和cta-group之间 */
  .product-showcase {
    order: 3;
    height: auto;
    margin-bottom: 1.5rem;
    transform-style: flat;
    perspective: none;
  }

  .product-stage {
    position: relative;
    animation: none;
    transform: none !important;
    width: 100%;
    max-width: 100%;
    display: flex;
    justify-content: center;
    margin-bottom: 1rem;
  }

  .product-image-wrapper {
    width: 85%;
    max-width: 450px;
    margin: 0 auto;
    position: relative;
    z-index: 2;
  }

  .hero-product-img {
    display: block;
    width: 100%;
    height: auto;
    max-width: 100%;
    object-fit: contain;
  }

  .spec-highlights {
    position: static;
    width: 90%;
    max-width: 400px;
    margin: 0 auto 2rem;
    transform: none;
    left: auto;
    bottom: auto;
    flex-direction: column;
    gap: 1.5rem;
    padding: 1.5rem;
    border-radius: 16px;
    box-sizing: border-box;
    background: rgba(0, 0, 0, 0.5);
    order: 3;
  }

  .cta-group {
    order: 4;
    flex-direction: row;
    width: 100%;
    margin-bottom: 1rem;
    margin-top: 0;
    gap: 1rem;
    justify-content: center;
  }

  .primary-button,
  .secondary-button {
    width: 140px;
    justify-content: center;
    font-size: 1rem;
    white-space: nowrap;
    padding: 1rem 1.75rem;
    box-sizing: border-box;
  }

  .secondary-button {
    padding: 0;
  }

  .secondary-button .button-inner {
    width: 100%;
    justify-content: center;
    padding: 1rem 1.75rem;
    font-size: 1rem;
    white-space: nowrap;
    box-sizing: border-box;
    min-height: 48px;
    display: flex;
    align-items: center;
  }

  .primary-button .button-text {
    white-space: nowrap;
  }

  .button-icon {
    width: 18px;
    height: 18px;
  }

  .primary-button {
    min-height: 48px;
    display: flex;
    align-items: center;
  }



  .spec-item {
    width: 100%;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding-bottom: 1.5rem;
    margin-bottom: 0;
  }

  .spec-item:last-child {
    border-bottom: none;
    padding-bottom: 0;
  }

  .spec-item::after {
    display: none;
  }

  .spec-value {
    font-size: 1.25rem;
    margin-bottom: 0.5rem;
  }

  .floating-badges {
    display: none !important;
  }

  .badge {
    display: none !important;
  }
}

@media (max-width: 480px) {
  .hero-container {
    padding: 100px 0 40px;
  }

  .main-title {
    font-size: 2rem;
    white-space: nowrap;
  }

  .sub-title {
    font-size: 1.1rem;
  }

  .scroll-indicator {
    margin: 1rem auto;
    margin-top: -0.05rem;
    margin-bottom: 5rem;
  }

  .cta-group {
    justify-content: space-evenly;
    padding: 0 1rem;
    margin-top: -3.5rem;
  }

  .primary-button,
  .secondary-button {
    width: 150px;
  }

  .feature-pills {
    margin-top: -2rem;
  }

  .product-showcase {
    margin-top: -3rem;
  }

  .tagline {
    font-size: 0.9rem;
    margin-bottom: 2rem;
  }

  .product-image-wrapper {
    width: 90%;
  }

  .spec-highlights {
    width: 95%;
    padding: 1.2rem;
    gap: 1.2rem;
    margin-top: 1rem;
  }

  .spec-item {
    padding-bottom: 1.2rem;
  }

  .spec-value {
    font-size: 1.1rem;
  }

  .spec-label {
    font-size: 0.7rem;
  }

  .badge {
    padding: 0.5rem 0.75rem;
    font-size: 0.75rem;
  }

  .title-group {
    margin-top:-6rem;
  }
}
